"""
Topic Service 健康检查

提供服务健康状态检查功能
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, Any
import httpx
import redis
from sqlalchemy import text

from .config import TopicServiceSettings
from .database import get_database_engine


class HealthChecker:
    """健康检查器"""
    
    def __init__(self, settings: TopicServiceSettings):
        self.settings = settings
    
    async def check_health(self) -> Dict[str, Any]:
        """执行完整的健康检查"""
        start_time = time.time()
        
        checks = {
            "database": await self._check_database(),
            "redis": await self._check_redis(),
            "manticore": await self._check_manticore(),
            "external_services": await self._check_external_services()
        }
        
        # 计算总体状态
        all_healthy = all(check["healthy"] for check in checks.values())
        status = "healthy" if all_healthy else "degraded"
        
        # 如果有关键服务不健康，标记为不健康
        critical_services = ["database"]
        critical_healthy = all(checks[service]["healthy"] for service in critical_services if service in checks)
        if not critical_healthy:
            status = "unhealthy"
        
        execution_time = (time.time() - start_time) * 1000
        
        return {
            "status": status,
            "timestamp": datetime.utcnow().isoformat(),
            "execution_time_ms": round(execution_time, 2),
            "checks": checks,
            "service": {
                "name": "Topic Service",
                "version": "1.0.0",
                "environment": self.settings.environment
            }
        }
    
    async def _check_database(self) -> Dict[str, Any]:
        """检查数据库连接"""
        try:
            engine = get_database_engine()
            start_time = time.time()
            
            with engine.connect() as connection:
                result = connection.execute(text("SELECT 1"))
                result.fetchone()
            
            response_time = (time.time() - start_time) * 1000
            
            return {
                "healthy": True,
                "response_time_ms": round(response_time, 2),
                "details": {
                    "driver": engine.dialect.name,
                    "pool_size": getattr(engine.pool, 'size', 'N/A'),
                    "checked_out": getattr(engine.pool, 'checkedout', 'N/A')
                }
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "details": {"connection": "failed"}
            }
    
    async def _check_redis(self) -> Dict[str, Any]:
        """检查Redis连接"""
        try:
            start_time = time.time()
            
            redis_client = redis.from_url(self.settings.redis_url, decode_responses=True)
            redis_client.ping()
            
            response_time = (time.time() - start_time) * 1000
            
            # 获取Redis信息
            info = redis_client.info()
            
            return {
                "healthy": True,
                "response_time_ms": round(response_time, 2),
                "details": {
                    "version": info.get("redis_version", "unknown"),
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory_human": info.get("used_memory_human", "unknown")
                }
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "details": {"connection": "failed"}
            }
    
    async def _check_manticore(self) -> Dict[str, Any]:
        """检查Manticore Search连接（使用 HTTP 异步 API，而非 pymysql）"""
        try:
            start_time = time.time()
            base = f"http://{self.settings.manticore_host}:{self.settings.manticore_http_port}"
            timeout = self.settings.health_check_timeout
            # 1) 检查 HTTP 根路径
            async with httpx.AsyncClient(timeout=timeout) as client:
                try:
                    r = await client.get(f"{base}/")
                    if r.status_code != 200:
                        return {
                            "healthy": False,
                            "response_time_ms": round((time.time() - start_time) * 1000, 2),
                            "details": {"error": f"HTTP {r.status_code}", "host": self.settings.manticore_host, "port": self.settings.manticore_http_port}
                        }
                    # optional: 获取版本信息
                    try:
                        data = r.json()
                        version = data.get("version", {}).get("number", "unknown") if isinstance(data, dict) else "unknown"
                    except Exception:
                        version = "unknown"
                except Exception as e:
                    return {
                        "healthy": False,
                        "response_time_ms": round((time.time() - start_time) * 1000, 2),
                        "details": {"error": str(e), "host": self.settings.manticore_host, "port": self.settings.manticore_http_port}
                    }
                # 2) 尝试通过 /cli 执行简单 SQL（SHOW STATUS 或 SHOW TABLES），以验证 SQL 接口
                try:
                    sql = "SHOW STATUS"
                    r2 = await client.post(f"{base}/cli", content=sql, headers={'Content-Type': 'text/plain'})
                    if r2.status_code == 200:
                        # 尝试解析文本或 json，保持兼容性
                        try:
                            payload = r2.json()
                        except Exception:
                            payload = r2.text
                        response_time = (time.time() - start_time) * 1000
                        return {
                            "healthy": True,
                            "response_time_ms": round(response_time, 2),
                            "details": {
                                "host": self.settings.manticore_host,
                                "http_port": self.settings.manticore_http_port,
                                "version": version,
                                "status_summary": (payload if isinstance(payload, (dict, list)) else str(payload))[:1024]
                            }
                        }
                    else:
                        return {
                            "healthy": False,
                            "response_time_ms": round((time.time() - start_time) * 1000, 2),
                            "details": {
                                "host": self.settings.manticore_host,
                                "http_port": self.settings.manticore_http_port,
                                "error": f"/cli returned HTTP {r2.status_code}"
                            }
                        }
                except Exception as e:
                    return {
                        "healthy": False,
                        "response_time_ms": round((time.time() - start_time) * 1000, 2),
                        "details": {"error": str(e), "host": self.settings.manticore_host, "http_port": self.settings.manticore_http_port}
                    }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "details": {
                    "host": self.settings.manticore_host,
                    "port": self.settings.manticore_http_port,
                    "connection": "failed"
                }
            }
    
    async def _check_external_services(self) -> Dict[str, Any]:
        """检查外部服务连接"""
        services = {
            "user_service": self.settings.user_service_url,
            "document_service": self.settings.document_service_url,
            "embedding_service": self.settings.embedding_service_url
        }
        
        results = {}
        overall_healthy = True
        
        async with httpx.AsyncClient(timeout=self.settings.health_check_timeout) as client:
            for service_name, service_url in services.items():
                try:
                    start_time = time.time()
                    
                    # 尝试访问健康检查端点
                    health_url = f"{service_url}/health"
                    response = await client.get(health_url)
                    
                    response_time = (time.time() - start_time) * 1000
                    
                    if response.status_code == 200:
                        results[service_name] = {
                            "healthy": True,
                            "response_time_ms": round(response_time, 2),
                            "status_code": response.status_code,
                            "url": health_url
                        }
                    else:
                        results[service_name] = {
                            "healthy": False,
                            "status_code": response.status_code,
                            "url": health_url,
                            "error": f"HTTP {response.status_code}"
                        }
                        overall_healthy = False
                        
                except Exception as e:
                    results[service_name] = {
                        "healthy": False,
                        "error": str(e),
                        "url": f"{service_url}/health"
                    }
                    overall_healthy = False
        
        return {
            "healthy": overall_healthy,
            "details": results
        }
    
    async def check_readiness(self) -> Dict[str, Any]:
        """检查服务就绪状态（更严格的检查）"""
        checks = await self.check_health()
        
        # 就绪检查需要所有关键服务都健康
        critical_services = ["database", "redis"]
        ready = all(
            checks["checks"][service]["healthy"] 
            for service in critical_services 
            if service in checks["checks"]
        )
        
        return {
            "ready": ready,
            "timestamp": datetime.utcnow().isoformat(),
            "critical_services": {
                service: checks["checks"][service]["healthy"]
                for service in critical_services
                if service in checks["checks"]
            }
        }
    
    async def check_liveness(self) -> Dict[str, Any]:
        """检查服务存活状态（最基本的检查）"""
        return {
            "alive": True,
            "timestamp": datetime.utcnow().isoformat(),
            "service": "Topic Service",
            "version": "1.0.0"
        }
