"""
Topic Service 数据库工具

提供数据库连接和会话管理
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
from typing import Generator

from .config import get_settings
from ..models.topic import Base


# 全局变量
engine = None
SessionLocal = None


def init_database():
    """初始化数据库"""
    global engine, SessionLocal
    
    settings = get_settings()
    
    # 创建数据库引擎 - 根据不同的数据库类型选择合适的参数
    engine_kwargs = {
        "echo": settings.database_echo,
    }
    db_url = settings.database_url or ""

    if db_url.startswith("sqlite"):
        # SQLite 在本地测试/CI 中使用 StaticPool 与 check_same_thread=False
        engine_kwargs.update({
            "poolclass": StaticPool,
            "connect_args": {"check_same_thread": False},
        })
    else:
        # 关系型数据库（Postgres/MySQL）适用连接池参数
        engine_kwargs.update({
            "pool_size": settings.database_pool_size,
            "pool_timeout": settings.database_pool_timeout,
        })

    engine = create_engine(
        db_url,
        **engine_kwargs
    )
    
    # 创建会话工厂
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # 创建表
    Base.metadata.create_all(bind=engine)
    
    print("✅ 数据库初始化完成")


def get_database_engine():
    """获取数据库引擎"""
    global engine
    if engine is None:
        init_database()
    return engine


def get_session_factory():
    """获取会话工厂"""
    global SessionLocal
    if SessionLocal is None:
        init_database()
    return SessionLocal


@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """获取数据库会话（上下文管理器）"""
    SessionLocal = get_session_factory()
    session = SessionLocal()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


def get_db_session_dependency():
    """FastAPI 依赖注入用的数据库会话"""
    SessionLocal = get_session_factory()
    session = SessionLocal()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


def create_tables():
    """创建数据库表"""
    engine = get_database_engine()
    Base.metadata.create_all(bind=engine)
    print("✅ 数据库表创建完成")


def drop_tables():
    """删除数据库表"""
    engine = get_database_engine()
    Base.metadata.drop_all(bind=engine)
    print("⚠️ 数据库表已删除")


def reset_database():
    """重置数据库（删除并重新创建表）"""
    print("🔄 重置数据库...")
    drop_tables()
    create_tables()
    print("✅ 数据库重置完成")


def check_database_connection() -> bool:
    """检查数据库连接"""
    try:
        engine = get_database_engine()
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


def get_database_info() -> dict:
    """获取数据库信息"""
    try:
        engine = get_database_engine()
        
        # 获取数据库URL（隐藏密码）
        url_str = str(engine.url)
        if "@" in url_str:
            # 隐藏密码部分
            parts = url_str.split("@")
            if ":" in parts[0]:
                user_pass = parts[0].split(":")
                if len(user_pass) >= 2:
                    url_str = f"{user_pass[0]}:***@{parts[1]}"
        
        # 获取连接池信息
        pool = engine.pool
        
        return {
            "url": url_str,
            "driver": engine.dialect.name,
            "pool_size": getattr(pool, 'size', 'N/A'),
            "checked_in": getattr(pool, 'checkedin', 'N/A'),
            "checked_out": getattr(pool, 'checkedout', 'N/A'),
            "overflow": getattr(pool, 'overflow', 'N/A'),
            "echo": engine.echo
        }
    except Exception as e:
        return {"error": str(e)}


def execute_sql(sql: str, params: dict = None) -> list:
    """执行原生SQL查询"""
    try:
        engine = get_database_engine()
        with engine.connect() as connection:
            result = connection.execute(sql, params or {})
            if result.returns_rows:
                return [dict(row) for row in result.fetchall()]
            else:
                return []
    except Exception as e:
        print(f"❌ SQL执行失败: {e}")
        raise


def get_table_info() -> dict:
    """获取表信息"""
    try:
        engine = get_database_engine()
        metadata = MetaData()
        metadata.reflect(bind=engine)
        
        tables_info = {}
        for table_name, table in metadata.tables.items():
            tables_info[table_name] = {
                "columns": [
                    {
                        "name": col.name,
                        "type": str(col.type),
                        "nullable": col.nullable,
                        "primary_key": col.primary_key,
                        "foreign_keys": [str(fk.target_fullname) for fk in col.foreign_keys]
                    }
                    for col in table.columns
                ],
                "indexes": [
                    {
                        "name": idx.name,
                        "columns": [col.name for col in idx.columns],
                        "unique": idx.unique
                    }
                    for idx in table.indexes
                ]
            }
        
        return tables_info
    except Exception as e:
        return {"error": str(e)}


# 数据库迁移相关函数
def backup_database(backup_path: str = None) -> str:
    """备份数据库（仅适用于SQLite）"""
    settings = get_settings()
    
    if not settings.database_url.startswith("sqlite"):
        raise ValueError("备份功能仅支持SQLite数据库")
    
    import shutil
    from datetime import datetime
    
    # 提取SQLite文件路径
    db_path = settings.database_url.replace("sqlite:///", "")
    
    if backup_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{db_path}.backup_{timestamp}"
    
    shutil.copy2(db_path, backup_path)
    print(f"✅ 数据库已备份到: {backup_path}")
    return backup_path


def restore_database(backup_path: str):
    """恢复数据库（仅适用于SQLite）"""
    settings = get_settings()
    
    if not settings.database_url.startswith("sqlite"):
        raise ValueError("恢复功能仅支持SQLite数据库")
    
    import shutil
    
    # 提取SQLite文件路径
    db_path = settings.database_url.replace("sqlite:///", "")
    
    # 关闭现有连接
    global engine
    if engine:
        engine.dispose()
        engine = None
    
    # 恢复文件
    shutil.copy2(backup_path, db_path)
    
    # 重新初始化
    init_database()
    print(f"✅ 数据库已从备份恢复: {backup_path}")


if __name__ == "__main__":
    # 测试数据库连接
    print("🔍 测试数据库连接...")
    
    if check_database_connection():
        print("✅ 数据库连接成功")
        
        # 显示数据库信息
        info = get_database_info()
        print(f"数据库信息: {info}")
        
        # 显示表信息
        tables = get_table_info()
        print(f"表信息: {list(tables.keys())}")
        
    else:
        print("❌ 数据库连接失败")
