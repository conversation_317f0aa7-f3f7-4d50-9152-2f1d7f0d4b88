"""
Manticore Search 集成服务（异步）
使用官方异步 ApiClient（manticoresearch）进行非阻塞调用。
与 demo/manticore_poc 的做法保持一致：使用 HTTP API 的异步客户端。
"""

from typing import List, Dict, Any, Optional
import asyncio

import manticoresearch
from manticoresearch.rest import ApiException

from ..models.topic import MemorySearchResult
from ..utils.config import TopicServiceSettings


class ManticoreService:
    """Manticore Search 异步服务封装"""

    def __init__(self, settings: TopicServiceSettings):
        self.settings = settings
        self.host = getattr(settings, "manticore_host", "127.0.0.1")
        self.http_port = int(getattr(settings, "manticore_http_port", getattr(settings, "manticore_http_port", 9308)))
        # 配置用于 ApiClient
        self.configuration = manticoresearch.Configuration(host=f"http://{self.host}:{self.http_port}")

    async def _search_api(self):
        async with manticoresearch.ApiClient(self.configuration) as api_client:
            yield manticoresearch.SearchApi(api_client)

    async def _index_api(self):
        async with manticoresearch.ApiClient(self.configuration) as api_client:
            yield manticoresearch.IndexApi(api_client)

    async def _utils_api(self):
        async with manticoresearch.ApiClient(self.configuration) as api_client:
            yield manticoresearch.UtilsApi(api_client)

    def _normalize_hits(self, resp) -> List:
        """
        兼容不同返回类型：有时是对象，有时是 dict。
        返回标准化的 hits 列表（可能为空）。
        """
        try:
            # 对象风格
            if hasattr(resp, "hits") and getattr(resp.hits, "hits", None) is not None:
                return resp.hits.hits
        except Exception:
            pass

        # dict 风格
        if isinstance(resp, dict):
            return resp.get("hits", {}).get("hits", []) or []
        return []

    async def search_topic_memory(self, topic_id: int, limit: int = 5) -> List[MemorySearchResult]:
        """搜索主题相关的长期记忆锚点（基于 docs_chunks 索引）"""
        try:
            async with manticoresearch.ApiClient(self.configuration) as api_client:
                search_api = manticoresearch.SearchApi(api_client)
                # 使用 query_string 进行简单的按 topic_id 搜索（与 demo 保持一致）
                request = {
                    "table": "docs_chunks",
                    "query": {"query_string": f"@topic_id {topic_id}"},
                    "limit": limit
                }
                resp = await search_api.search(request)
                hits = self._normalize_hits(resp)
                results: List[MemorySearchResult] = []
                for h in hits:
                    # 支持对象属性或 dict 访问
                    src = getattr(h, "source", None) or h.get("_source", {}) if isinstance(h, dict) else {}
                    score = getattr(h, "score", None) or h.get("_score", 0.0) if isinstance(h, dict) else 0.0
                    # 保证 id/content 字段存在
                    mid = src.get("id") if isinstance(src, dict) else None
                    content = src.get("content") if isinstance(src, dict) else ""
                    results.append(MemorySearchResult(
                        id=int(mid) if mid is not None else 0,
                        content=content or "",
                        relevance_score=float(score or 0.0),
                        metadata={"topic_id": topic_id}
                    ))
                return results
        except ApiException as e:
            print(f"Manticore API 异常(search_topic_memory): {e}")
            return []
        except Exception as e:
            print(f"Manticore 搜索失败(search_topic_memory): {e}")
            return []

    async def search_documents_by_topic(self, topic_id: int, query: str, limit: int = 10) -> List[MemorySearchResult]:
        """按主题和查询词搜索"""
        try:
            async with manticoresearch.ApiClient(self.configuration) as api_client:
                search_api = manticoresearch.SearchApi(api_client)
                # 将 topic_id 与 query 合并为 query_string
                qstr = f"@topic_id {topic_id} AND {query}"
                request = {
                    "table": "docs_chunks",
                    "query": {"query_string": qstr},
                    "limit": limit
                }
                resp = await search_api.search(request)
                hits = self._normalize_hits(resp)
                results: List[MemorySearchResult] = []
                for h in hits:
                    src = getattr(h, "source", None) or h.get("_source", {}) if isinstance(h, dict) else {}
                    score = getattr(h, "score", None) or h.get("_score", 0.0) if isinstance(h, dict) else 0.0
                    mid = src.get("id") if isinstance(src, dict) else None
                    content = src.get("content") if isinstance(src, dict) else ""
                    results.append(MemorySearchResult(
                        id=int(mid) if mid is not None else 0,
                        content=content or "",
                        relevance_score=float(score or 0.0),
                        metadata={"topic_id": topic_id, "query": query}
                    ))
                return results
        except ApiException as e:
            print(f"Manticore API 异常(search_documents_by_topic): {e}")
            return []
        except Exception as e:
            print(f"Manticore 文档搜索失败(search_documents_by_topic): {e}")
            return []

    async def index_topic_document(
        self,
        topic_id: int,
        document_id: int,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """把主题文档索引到 docs_chunks 索引（使用 IndexApi.insert 或 replace）"""
        try:
            async with manticoresearch.ApiClient(self.configuration) as api_client:
                index_api = manticoresearch.IndexApi(api_client)
                doc = {
                    "id": str(document_id),
                    "doc_id": str(document_id),
                    "content": content,
                    "metadata": metadata or {}
                }
                # 如果有 embedding 在 metadata 中，附带它
                if metadata and metadata.get("embedding") is not None:
                    doc["embedding"] = metadata.get("embedding")
                # 使用 replace 可以做到幂等性；这里用 replace 保证同一 doc_id 会被替换
                try:
                    await index_api.replace({"table": "docs_chunks", "doc": doc})
                except Exception:
                    # fallback insert if replace 不可用
                    await index_api.insert({"table": "docs_chunks", "doc": doc})
                return True
        except ApiException as e:
            print(f"Manticore API 异常(index_topic_document): {e}")
            return False
        except Exception as e:
            print(f"Manticore 索引失败(index_topic_document): {e}")
            return False

    async def remove_topic_document(self, topic_id: int, document_id: int) -> bool:
        """从索引中删除指定文档（基于 search+replace/delete API）"""
        try:
            async with manticoresearch.ApiClient(self.configuration) as api_client:
                # 通过 SQL 接口删除（更通用）
                utils_api = manticoresearch.UtilsApi(api_client)
                sql = f"DELETE FROM docs_chunks WHERE topic_id = '{topic_id}' AND doc_id = '{document_id}'"
                await utils_api.sql(sql)
                return True
        except ApiException as e:
            print(f"Manticore API 异常(remove_topic_document): {e}")
            return False
        except Exception as e:
            print(f"Manticore 删除索引失败(remove_topic_document): {e}")
            return False

    async def get_topic_document_count(self, topic_id: int) -> int:
        """获取主题在索引中的文档数量"""
        try:
            async with manticoresearch.ApiClient(self.configuration) as api_client:
                search_api = manticoresearch.SearchApi(api_client)
                request = {
                    "table": "docs_chunks",
                    "query": {"query_string": f"@topic_id {topic_id}"},
                    "limit": 0
                }
                resp = await search_api.search(request)
                # resp 可能是对象或 dict，尝试多路解析
                if isinstance(resp, dict):
                    total = resp.get("hits", {}).get("total", {}).get("value", 0)
                    return int(total or 0)
                # 对象风格
                hits = getattr(resp, "hits", None)
                if hits is not None:
                    # some implementations expose total as hits.total or hits.total.value
                    total = getattr(hits, "total", None)
                    if total is None:
                        return 0
                    if hasattr(total, "value"):
                        return int(total.value or 0)
                    try:
                        return int(total)
                    except Exception:
                        return 0
                return 0
        except Exception as e:
            print(f"Manticore 统计失败(get_topic_document_count): {e}")
            return 0

    async def search_similar_topics(
        self,
        content: str,
        exclude_topic_id: Optional[int] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """基于内容搜索相似主题（返回简单 dict 列表）"""
        try:
            async with manticoresearch.ApiClient(self.configuration) as api_client:
                search_api = manticoresearch.SearchApi(api_client)
                # 使用 match 或 knn 混合的查询可以在未来扩展；目前使用 query_string
                request = {
                    "table": "docs_chunks",
                    "query": {"query_string": content},
                    "limit": limit
                }
                resp = await search_api.search(request)
                hits = self._normalize_hits(resp)
                out = []
                for h in hits:
                    src = getattr(h, "source", None) or h.get("_source", {}) if isinstance(h, dict) else {}
                    topic_id = src.get("topic_id") or src.get("metadata", {}).get("topic_id")
                    score = getattr(h, "score", None) or h.get("_score", 0.0) if isinstance(h, dict) else 0.0
                    out.append({"topic_id": int(topic_id) if topic_id is not None else None, "score": float(score)})
                return out
        except Exception as e:
            print(f"Manticore 相似主题搜索失败(search_similar_topics): {e}")
            return []

    def test_connection(self) -> bool:
        """同步测试连接（尽量保持兼容）"""
        try:
            # 使用同步 HTTP 请求检查根路径
            import requests
            resp = requests.get(f"http://{self.host}:{self.http_port}/")
            return resp.status_code == 200
        except Exception as e:
            print(f"Manticore 连接测试失败(test_connection): {e}")
            return False
