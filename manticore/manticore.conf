#
# Minimal Manticore Search configuration file (with predeclared docs_chunks table)
#
# NOTE:
# - This configuration intentionally pre-declares the `docs_chunks` table so that
#   the Manticore instance loads it as a local table on startup. This avoids the
#   "data_dir cannot be mixed with table declarations" runtime limitation.
# - If you prefer runtime CREATE TABLE via /cli, remove the table block below and
#   ensure data_dir is not set in this file (use a different config without table declarations).
#
searchd
{
    # data_dir intentionally left unset here when using predeclared tables.
    # If you want runtime table creation instead, remove the table block below and
    # set: data_dir = /var/lib/manticore
    #
    # listen on all interfaces
    listen = 9306:mysql41
    listen = 9312
    listen = 9308:http

    # PID file
    pid_file = /var/run/manticore/searchd.pid

    # disable binlog for simplicity
    binlog_path =
}

# Predeclared table for docs_chunks (columnar/RT hybrid used for POC)
table docs_chunks
{
    type = rt
    # path under manticore data directory where RT table files will be stored
    path = /var/lib/manticore/docs_chunks

    # fields
    rt_field = content
    rt_field = title

    # attributes
    rt_attr_uint = topic_id
    rt_attr_uint = document_id
    rt_attr_string = category
    rt_attr_timestamp = created_at

    # tokenization / charset
    charset_table = 0..9, A..Z->a..z, _, a..z
    min_word_len = 2

    # optional options
    morphology = stem_en
}
