{"permissions": {"allow": ["Bash(pip install:*)", "Bash(python3 -m pip install:*)", "<PERSON><PERSON>(uv init:*)", "<PERSON><PERSON>(uv venv:*)", "Bash(uv sync:*)", "Bash(uv add:*)", "<PERSON><PERSON>(uv run:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(python test:*)", "Bash(cp:*)", "Bash(python -m pytest app/tests/ -v)", "<PERSON><PERSON>(source:*)", "Bash(PYTHONPATH=.. python -m pytest app/tests/ -v --tb=short)", "Bash(PYTHONPATH=. python -m pytest backend/app/tests/ -v --tb=short)", "Bash(PYTHONPATH=. python3 -m pytest backend/app/tests/ -v --tb=short)", "Bash(PYTHONPATH=.. python3 -m pytest app/tests/test_documents_integration.py -v)", "Bash(PYTHONPATH=.. python -m pytest app/tests/test_documents_integration.py -v)"], "deny": [], "ask": [], "additionalDirectories": ["/Users/<USER>/claude-workspace"]}}