# Manticore Search minimal configuration for Topic POC (no embedded table declarations)
#
# This file intentionally excludes any 'table { ... }' declarations so that
# runtime CREATE TABLE via the HTTP /cli interface works correctly. If you
# need persistent predeclared tables, add them in a separate configuration
# and avoid mixing 'data_dir' with inline table declarations.
#
searchd {
    listen = 9306:mysql41
    listen = 9308:http
    listen = 9312

    log = /var/log/manticore/searchd.log
    query_log = /var/log/manticore/query.log
    pid_file = /var/run/manticore/searchd.pid
    data_dir = /var/lib/manticore

    # 查询缓存
    qcache_max_bytes = 256M
    qcache_thresh_msec = 1000
    qcache_ttl_sec = 3600

    # 网络设置
    client_timeout = 300
    max_packet_size = 128M

    # 性能优化
    max_children = 0
    max_matches = 1000
}

# Notes:
# - Do NOT declare tables inline here if data_dir is set.
# - Use the HTTP /cli endpoint to create runtime tables when data_dir is in use:
#     curl -X POST http://localhost:9308/cli -H "Content-Type: text/plain" --data "CREATE TABLE docs_chunks (id string, doc_id string, content text, metadata json, embedding float_vector(384)) engine='columnar'"
# - For persistent pre-defined tables, place table declarations in a config
#   without data_dir or use a dedicated configuration file mounted into the container.
