# Topic 模块（Topic Service）

模块概述
Topic Service 提供主题（topic）管理与文档关联的核心功能，包括：
- 主题 CRUD（创建/读取/更新/删除）
- 文档与主题的关联与取消关联
- 主题统计（文档数量、活跃度等）
- 与 Manticore Search 集成，用于长期记忆锚点与向量/关键词检索
- 健康检查与监控端点

当前状态
- 验证状态：✅ 开发可用（模块代码与核心测试已在本地通过）
- 已验证环境：
  - 历史版本（commit 72dc62f）在本地 SQLite 下通过 smoke test
  - 当前 main 分支在本地通过大部分集成测试（使用 SQLite）并能与本地 Manticore POC 互通（需按文档初始化 Manticore 表）
- 未决项：Manticore 在某些宿主配置下需要预声明表或调整配置（见故障排除）

已验证功能清单
- 主题创建 /api/v1/topics (POST)
- 获取主题 /api/v1/topics/{topic_id} (GET)
- 列表 /api/v1/topics (GET)
- 更新 /api/v1/topics/{topic_id} (PUT)
- 删除 /api/v1/topics/{topic_id} (DELETE)
- 文档关联 /api/v1/topics/{topic_id}/documents (POST)
- 主题记忆检索 /api/v1/topics/{topic_id}/memory (GET) （需 Manticore 表与索引就绪）
- 健康检查 /health (GET)

架构组件说明
- FastAPI 应用层：`services/topic/api`（FastAPI 路由与依赖）
- 业务逻辑层：`services/topic/services`（`TopicService`、`ManticoreService`）
- 数据模型：
  - SQLAlchemy 模型：`services/topic/models/topic.py`
  - Pydantic DTO：同文件内定义（TopicCreate/TopicResponse/MemorySearchResult 等）
- 数据库层：`services/topic/utils/database.py`（支持 SQLite 开发与 Postgres 生产）
- 配置：`services/topic/utils/config.py`（通过 TOPIC_ 前缀环境变量覆盖）
- Manticore 集成：
  - 异步官方客户端 `manticoresearch`（HTTP API via ApiClient）
  - 客户端实现见 [`services/topic/services/manticore_service.py`](services/topic/services/manticore_service.py:1)

配置文件详解（常用环境变量）
- TOPIC_API_HOST / TOPIC_API_PORT: API 监听地址与端口
- TOPIC_DATABASE_URL: SQLAlchemy 数据库 URL（示例 sqlite:///./topic_service.db 或 ********************************/db）
- TOPIC_REDIS_URL: Redis URL
- TOPIC_MANTICORE_HOST / TOPIC_MANTICORE_HTTP_PORT: Manticore 主机与 HTTP 端口
- 更多配置见 [`services/topic/utils/config.py`](services/topic/utils/config.py:1)

启动和测试方法（本地开发）
1. 安装依赖（模块范围）
   - cd services/topic
   - pip install -r requirements.txt
2. 快速启动（开发模式，使用 SQLite）
   - TOPIC_DATABASE_URL="sqlite:///./topic_service_dev.db" python run_service.py
   - 或：uvicorn api.main:app --host 0.0.0.0 --port 9004 --reload
3. 运行简单测试（无需外部依赖）
   - cd services/topic
   - python simple_test.py
4. 运行完整集成测试（需要数据库与可选的 Manticore）
   - 对于本地 SQLite 验证：
     - export TOPIC_DATABASE_URL="sqlite:///./topic_service_test.db"
     - python -m services.topic.test_service
   - 对于真实 Postgres + Manticore 验证：先确保 Postgres 与 Manticore 启动并配置正确，再运行相同测试命令
5. Manticore 初始化（若使用本地 Manticore）
   - 如果 Manticore 配置允许 runtime 表创建（无 data_dir 与预声明冲突），使用：
     - curl -X POST "http://{MANTICORE_HOST}:{MANTICORE_HTTP_PORT}/cli" -H "Content-Type: text/plain" --data "CREATE TABLE docs_chunks (id string, doc_id string, content text, metadata json, embedding float_vector(384)) engine='columnar'"
   - 或者，若 Manticore 以配置文件预声明表为准，请参照 docs/manticore/INIT.md 进行操作（仓库中有示例配置）

API 端点文档（摘要）
- POST /api/v1/topics
  - Body: { "title": "...", "description": "...", "user_id": 123 }
  - 返回: TopicResponse
- GET /api/v1/topics/{topic_id}
  - 返回: TopicResponse（含 document_count）
- POST /api/v1/topics/{topic_id}/documents
  - Body: { "document_id": 456 }
  - 返回: { "success": true }
- GET /api/v1/topics/{topic_id}/memory
  - 返回: TopicMemoryResponse（依赖 Manticore）

使用示例（Python requests）
- 创建主题：
  - requests.post("http://localhost:9004/api/v1/topics", json={"title":"t","description":"d","user_id":1})
- 检查健康：
  - requests.get("http://localhost:9004/health").json()

故障排除指南（常见问题）
- 问题：docker compose 启动时报错 Bind for 0.0.0.0:6379 failed: port is already allocated  
  - 原因：宿主机已有 Redis 占用 6379。  
  - 解决：停止占用容器或使用 docker-compose.override.yml 修改 demo 端口映射（已在 demo/topic_poc/docker-compose.override.yml 提供示例），或启动服务时不绑定宿主端口。
- 问题：Manticore search 返回 {"error":"unknown local table(s) 'docs_chunks'"}  
  - 原因：Manticore 实例可能以配置文件预声明或以 data_dir 模式运行，/cli 创建的表未被识别。  
  - 解决方案：
    - 如果希望 runtime CREATE：确保 Manticore 配置文件不同时包含 data_dir 与 inline table 声明（参见 `manticore/manticore.conf`）；使用 /cli 创建表后再调用 search。
    - 或在 Manticore 配置中预声明 `docs_chunks` 并重启实例（我已在仓库 `manticore/manticore.conf` 提供了预声明示例）。
- 问题：测试因 Postgres 认证失败（password authentication failed）  
  - 解决：临时使用 SQLite： export TOPIC_DATABASE_URL="sqlite:///./topic_service_test.db" 并运行测试；或修正 Postgres 的用户/密码与 TOPIC_DATABASE_URL。

性能优化建议
- 把 Manticore ApiClient 实例化改为单例或通过依赖注入传入，避免每次搜索时创建销毁 ApiClient。
- 在数据库层使用连接池（已为 Postgres 配置），对 SQLite 的本地测试使用 StaticPool。
- 对批量插入 / 向量索引使用批处理（bulk API），避免逐条插入。

版本兼容性信息
- Python 3.11 已被验证（测试环境）
- manticoresearch 客户端：推荐使用与本地 Manticore Server 版本兼容的 `manticoresearch` 包（仓库中使用 9.x 系列示例）
- SQLAlchemy 2.x（注意 SQLAlchemy dialect 与 DB driver 的兼容性，如 psycopg2）

变更记录与来源
- 代码改动（主要）：  
  - [`services/topic/services/manticore_service.py`](services/topic/services/manticore_service.py:1) — 使用官方异步 ApiClient，并与 demo 对齐（使用 "table" 字段）  
  - [`services/topic/utils/database.py`](services/topic/utils/database.py:1) — 兼容 SQLite 的 engine 创建逻辑（区分 pool 参数）  
  - [`services/topic/utils/health.py`](services/topic/utils/health.py:117) — Manticore 健康检查改为 HTTP /cli 风格  
  - [`manticore/manticore.conf`](manticore/manticore.conf:1) — 示例配置（包含预声明 `docs_chunks` 的示例）
- 测试输出与日志：见 /tmp 下的测试日志（例如 /tmp/topic_service_after_predeclare.log）

快速开始（5 分钟验证）
1. 在仓库根目录：
   - export TOPIC_DATABASE_URL="sqlite:///./topic_service_test.db"
   - python -m services.topic.test_service
   - 若使用 Manticore：确保 Manticore 可达并已创建 `docs_chunks` 表（参见上文）
2. 若遇到端口冲突，编辑 `demo/topic_poc/docker-compose.override.yml`（仓库已包含示例），然后以 override 启动：  
   - docker compose -f demo/topic_poc/docker-compose.yml -f demo/topic_poc/docker-compose.override.yml up -d

后续工作与建议里程碑
- 把 Manticore ApiClient 改成可复用单例/注入（改进性能）
- 在 docs 中增加 CI 流程与本地一键初始化脚本（makefile 或 demo/start.sh）
- 对 repo 中其他使用 pymysql 的服务（非 Topic 模块）做审计，决定是否移除或替换
